/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable prettier/prettier */
/*
AWS S3 File Service Implementation
*/

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { randomBytes } from 'crypto';
import * as path from 'path';
import ffmpeg from 'fluent-ffmpeg';
import { PassThrough } from 'stream';
import { Response } from 'express';
import { LogService } from 'src/log/log.service';

@Injectable()
export class AwsService {
    private s3Client: S3Client;
    private bucketName: string;

    constructor(private configService: ConfigService, private logService: LogService) {
        // Initialize S3 client with configuration
        this.s3Client = new S3Client({
            region: this.configService.get('AWS_REGION') || 'eu-central-1',
            credentials: {
                accessKeyId: this.configService.get('AWS_ACCESS_KEY'),
                secretAccessKey: this.configService.get('AWS_SECRET_SECRET_KEY'),
            },
        });
        
        this.bucketName = this.configService.get('AWS_BUCKET_NAME');
    }

    /**
     * Get a signed URL for a file in S3 bucket
     * @param filename - The filename/key in S3
     * @returns Promise<string> - The signed URL
     */
    async getS3FilePublicUrl(filename: string): Promise<string> {
        try {
            const command = new GetObjectCommand({
                Bucket: this.bucketName,
                Key: filename,
            });

            // Generate signed URL with 1 year expiration
            const signedUrl = await getSignedUrl(this.s3Client, command, {
                expiresIn: 365 * 24 * 60 * 60, // 1 year in seconds
            });

            this.logService.log('Generated S3 signed URL for:', filename);
            return signedUrl;
        } catch (error) {
            this.logService.error('Error generating S3 signed URL:', error);
            throw new Error('Failed to generate signed URL');
        }
    }

    /**
     * Stream a file from S3 to the response
     * @param filename - The filename/key in S3
     * @param res - Express response object
     */
    async getFromS3(filename: string, res: Response): Promise<void> {
        try {
            this.logService.log('Fetching file from S3:', filename);

            const command = new GetObjectCommand({
                Bucket: this.bucketName,
                Key: filename,
            });

            const response = await this.s3Client.send(command);

            if (!response.Body) {
                throw new Error('No file body received from S3');
            }

            // Set appropriate headers for the response
            res.setHeader('Content-disposition', `attachment; filename=${filename}`);
            res.setHeader('Content-type', response.ContentType || 'application/octet-stream');
            res.setHeader('Content-length', response.ContentLength || 0);

            // Stream the file to the response
            const stream = response.Body as NodeJS.ReadableStream;
            stream.pipe(res);

        } catch (error) {
            this.logService.error('Error fetching file from S3:', error);
            res.status(500).send('Internal Server Error');
        }
    }

    /**
     * Upload a file to S3
     * @param file - The uploaded file from multer
     * @returns Promise<object> - File information including URL
     */
    async s3Upload(file: Express.Multer.File): Promise<object> {
        try {
            // Generate a unique name for the file
            const randomName = `${Date.now()}-${randomBytes(16).toString('hex')}`;
            const isAudioFile = file.mimetype.startsWith('audio/');
            const fileExtension = isAudioFile ? '.wav' : path.extname(file.originalname);
            const objectKey = `${randomName}${fileExtension}`;

            let fileBuffer = file.buffer;
            let contentType = file.mimetype;

            // Handle audio file conversion if needed
            if (isAudioFile) {
                fileBuffer = await this.convertAudioToWav(file.buffer);
                contentType = 'audio/wav';
            }

            // Upload to S3
            const command = new PutObjectCommand({
                Bucket: this.bucketName,
                Key: objectKey,
                Body: fileBuffer,
                ContentType: contentType,
                Metadata: {
                    originalName: file.originalname,
                    uploadedAt: new Date().toISOString(),
                },
            });

            const uploadResult = await this.s3Client.send(command);

            // Generate a signed URL for the uploaded file
            const publicUrl = await this.getS3FilePublicUrl(objectKey);

            // Return file information
            const fileInfo = {
                filename: objectKey,
                size: fileBuffer.length,
                type: contentType,
                url: publicUrl,
                originalname: file.originalname,
                etag: uploadResult.ETag,
            };

            this.logService.log('File uploaded to S3:', fileInfo);
            return fileInfo;

        } catch (error) {
            this.logService.error('Error uploading file to S3:', error);
            throw new Error('Failed to upload file to S3');
        }
    }

    /**
     * Delete a file from S3
     * @param filename - The filename/key in S3
     * @returns Promise<boolean> - Success status
     */
    async deleteFromS3(filename: string): Promise<boolean> {
        try {
            const command = new DeleteObjectCommand({
                Bucket: this.bucketName,
                Key: filename,
            });

            await this.s3Client.send(command);
            this.logService.log('File deleted from S3:', filename);
            return true;

        } catch (error) {
            this.logService.error('Error deleting file from S3:', error);
            return false;
        }
    }

    /**
     * Convert audio buffer to WAV format using ffmpeg
     * @param audioBuffer - The input audio buffer
     * @returns Promise<Buffer> - The converted WAV buffer
     */
    private async convertAudioToWav(audioBuffer: Buffer): Promise<Buffer> {
        return new Promise((resolve, reject) => {
            const bufferStream = new PassThrough();
            const outputStream = new PassThrough();
            const chunks: Buffer[] = [];

            bufferStream.end(audioBuffer);

            outputStream.on('data', (chunk) => chunks.push(chunk));
            outputStream.on('end', () => resolve(Buffer.concat(chunks)));
            outputStream.on('error', reject);

            ffmpeg(bufferStream)
                .toFormat('wav')
                .pipe(outputStream)
                .on('error', reject);
        });
    }

    /**
     * Check if S3 bucket is accessible
     * @returns Promise<boolean> - Accessibility status
     */
    async checkS3Connection(): Promise<boolean> {
        try {
            // Try to list objects in the bucket (with limit 1 to minimize cost)
            const command = new ListObjectsV2Command({
                Bucket: this.bucketName,
                MaxKeys: 1,
            });

            await this.s3Client.send(command);
            this.logService.log('S3 connection successful');
            return true;

        } catch (error) {
            this.logService.error('S3 connection failed:', error);
            return false;
        }
    }
}
