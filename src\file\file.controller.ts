/* eslint-disable prettier/prettier */
/*  
https://docs.nestjs.com/controllers#controllers
*/

import {
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Req,
    Res,
    UploadedFile,
    UseInterceptors
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request, Response } from 'express';
import { diskStorage } from 'multer';
import { FileService } from './file.service';
import { ApiTags } from '@nestjs/swagger';
import { LogService } from 'src/log/log.service';

@ApiTags('File V1')
@Controller('v1/file')
export class FileController {
    constructor(private fileService: FileService, private logService: LogService) { }
    @Post()
    @UseInterceptors(FileInterceptor('file', {
        storage: diskStorage({
            destination: './uploads',
            filename: function (req, file, cb) {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
                this.logService.log('unique: ', uniqueSuffix);
                const parts = file.originalname?.split('.');
                const ext = parts[parts.length - 1];
                cb(null, file.fieldname + '-' + uniqueSuffix + '.' + ext);
            },
        })
    }))

    uploadFile(@UploadedFile() file: Express.Multer.File, @Req() req: Request) {
        // this.logService.log('file upload route: ', file);
        const response = {
            originalname: file.originalname,
            filename: file.filename,
            url: req.url + '/' + file.filename,
            type: file.mimetype,
            size: file.size,
            message: file.originalname + ' uploaded successfully'
        };
        this.logService.log('response: ', response);

        return response;
        // return this.fileService.upload(req, res);
    }
    @Get(':id')
    async getFile(@Param('id') filename: string, @Res() res: Response) {
        this.logService.log('file Key: ', filename);
        return this.fileService.getFromS3(filename, res)
        // return { url: await this.fileService.getFromGCS(key) }
        // @Param('id') key: string,

        // return res.sendFile(id, { root: 'uploads' });
    }
    // AWS S3 endpoints
    @Post('s3upload')
    @UseInterceptors(FileInterceptor('file', {}))
    s3Upload(@UploadedFile() file: Express.Multer.File) {
        console.log('S3 upload file: ', file)
        return this.fileService.s3Upload(file)
    }

    @Get('s3/url/:id')
    async getS3FileUrl(@Param('id') filename: string) {
        this.logService.log('Getting S3 signed URL for file: ', filename);
        const url = await this.fileService.getS3FilePublicUrl(filename);
        return { url };
    }

    @Delete('s3/:id')
    async deleteS3File(@Param('id') filename: string) {
        this.logService.log('Deleting S3 file: ', filename);
        const success = await this.fileService.deleteFromS3(filename);
        return { success, message: success ? 'File deleted successfully' : 'Failed to delete file' };
    }

    @Get('health/s3')
    async checkS3Health() {
        const isConnected = await this.fileService.checkS3Connection();
        return {
            status: isConnected ? 'healthy' : 'unhealthy',
            service: 'AWS S3',
            timestamp: new Date().toISOString()
        };
    }

    // GCS endpoints (keeping existing functionality)
    @Get('gcs/url/:id')
    async getGCSFileUrl(@Param('id') filename: string) {
        this.logService.log('Getting GCS signed URL for file: ', filename);
        const url = await this.fileService.getGCSFilePublicUrl(filename);
        return { url };
    }

}
